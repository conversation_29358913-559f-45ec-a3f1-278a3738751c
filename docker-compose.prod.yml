version: '3.8'

services:
  fl-nextjs-core:
    build:
      context: .
      target: production
    container_name: ${SERVICE_NAME:-fl-nextjs-core-prod}
    environment:
      - NODE_ENV=production
      - NEXT_TELEMETRY_DISABLED=1
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.${SERVICE_NAME:-fl-nextjs-core-prod}.rule=Host(`${TRAEFIK_HOST:-fl-nextjs-core.prod}`)"
      - "traefik.http.routers.${SERVICE_NAME:-fl-nextjs-core-prod}.entrypoints=${TRAEFIK_ENTRYPOINT:-websecure}"
      - "traefik.http.services.${SERVICE_NAME:-fl-nextjs-core-prod}.loadbalancer.server.port=3000"
      # Production: SSL redirect and security headers
      - "traefik.http.routers.${SERVICE_NAME:-fl-nextjs-core-prod}.tls=true"
      - "traefik.http.routers.${SERVICE_NAME:-fl-nextjs-core-prod}.tls.certresolver=letsencrypt"
      - "traefik.http.middlewares.${SERVICE_NAME:-fl-nextjs-core-prod}-security.headers.frameDeny=true"
      - "traefik.http.middlewares.${SERVICE_NAME:-fl-nextjs-core-prod}-security.headers.contentTypeNosniff=true"
      - "traefik.http.middlewares.${SERVICE_NAME:-fl-nextjs-core-prod}-security.headers.browserXssFilter=true"
      - "traefik.http.routers.${SERVICE_NAME:-fl-nextjs-core-prod}.middlewares=${SERVICE_NAME:-fl-nextjs-core-prod}-security"
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://0.0.0.0:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

networks:
  default:
    external:
      name: ${DOCKER_NETWORK:-production}
