/**
 * Builder base para construir modelos API con esquemas Zod.
 * Permite definir params, filters, body y response de forma encadenada.
 * Utilizado como base por los builders especializados.
 */
import { ZodObject } from "zod";
import { BaseApiModel, ApiSchemaModel } from "../base/baseApiModel";

export class ApiModelBaseBuilder {
  readonly _name: string;

  protected schemas: Partial<ApiSchemaModel> = {};

  constructor(_name: string) {
    this._name = _name;
  }

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  setParams(schema: ZodObject<any>) {
    this.schemas.params = schema;
    return this;
  }

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  setFilters(schema: ZodObject<any>) {
    this.schemas.filters = schema;
    return this;
  }

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  setBody(schema: ZodObject<any>) {
    this.schemas.body = schema;
    return this;
  }

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  setResponse(schema: ZodObject<any>) {
    this.schemas.response = schema;
    return this;
  }

  build() {
    return new BaseApiModel(this._name, this.schemas);
  }
}
