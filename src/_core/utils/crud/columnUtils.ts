/**
 * Utilities for column management in CRUD operations
 * Provides functions for column configuration, visibility, and processing
 */

import { ColumnConfig, DataRow, ColumnRenderer } from './dataTypes';

/**
 * Generates default column configuration from data
 * @param data - Array of data rows
 * @param excludeColumns - Columns to exclude from auto-generation
 * @returns Array of column configurations
 */
export function generateDefaultColumns(
  data: DataRow[],
  excludeColumns: string[] = ['__actions']
): ColumnConfig[] {
  if (!data || data.length === 0) return [];

  const firstRow = data[0];
  return Object.keys(firstRow)
    .filter(key => !excludeColumns.includes(key))
    .map(key => ({
      key,
      label: formatColumnLabel(key),
    }));
}

/**
 * Formats a column key into a human-readable label
 * @param key - The column key
 * @returns Formatted label
 */
export function formatColumnLabel(key: string): string {
  return key
    .replace(/([A-Z])/g, ' $1') // Add space before capital letters
    .replace(/[_-]/g, ' ') // Replace underscores and hyphens with spaces
    .replace(/\b\w/g, l => l.toUpperCase()) // Capitalize first letter of each word
    .trim();
}

/**
 * Checks if a column should be visible
 * @param column - Column configuration
 * @param hiddenRenderer - The renderer used for hidden columns
 * @returns True if column should be visible
 */
export function isColumnVisible(column: ColumnConfig, hiddenRenderer?: ColumnRenderer): boolean {
  return column.renderer !== hiddenRenderer;
}

/**
 * Filters columns to only visible ones
 * @param columns - Array of column configurations
 * @param hiddenRenderer - The renderer used for hidden columns
 * @returns Array of visible columns
 */
export function getVisibleColumns(columns: ColumnConfig[], hiddenRenderer?: ColumnRenderer): ColumnConfig[] {
  return columns.filter(column => isColumnVisible(column, hiddenRenderer));
}

/**
 * Sorts columns by a specified order
 * @param columns - Array of column configurations
 * @param order - Array of column keys in desired order
 * @returns Sorted array of columns
 */
export function sortColumnsByOrder(columns: ColumnConfig[], order: string[]): ColumnConfig[] {
  const orderMap = new Map(order.map((key, index) => [key, index]));
  
  return [...columns].sort((a, b) => {
    const orderA = orderMap.get(a.key) ?? Number.MAX_SAFE_INTEGER;
    const orderB = orderMap.get(b.key) ?? Number.MAX_SAFE_INTEGER;
    return orderA - orderB;
  });
}

/**
 * Merges default columns with custom column configurations
 * @param defaultColumns - Auto-generated columns
 * @param customColumns - Custom column configurations
 * @returns Merged column configurations
 */
export function mergeColumnConfigs(
  defaultColumns: ColumnConfig[],
  customColumns: ColumnConfig[]
): ColumnConfig[] {
  const customMap = new Map(customColumns.map(col => [col.key, col]));
  
  return defaultColumns.map(defaultCol => {
    const custom = customMap.get(defaultCol.key);
    return custom ? { ...defaultCol, ...custom } : defaultCol;
  });
}

/**
 * Validates column configuration
 * @param columns - Array of column configurations
 * @returns Array of validation errors (empty if valid)
 */
export function validateColumns(columns: ColumnConfig[]): string[] {
  const errors: string[] = [];
  const keys = new Set<string>();

  for (const column of columns) {
    if (!column.key) {
      errors.push('Column missing required "key" property');
      continue;
    }

    if (keys.has(column.key)) {
      errors.push(`Duplicate column key: "${column.key}"`);
    }
    keys.add(column.key);
  }

  return errors;
}
