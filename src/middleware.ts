import { NextRequest, NextResponse } from "next/server";

import { verifyToken } from "@/_core/lib/context/auth/authTokenService";
import { submitTokenRefresh } from "@/_lib/data/model/action/token/token";

import { UnauthorizedError } from "@/_core/lib/context/error/badRequestError";

export async function middleware(request: NextRequest) {
  const token = request.cookies.get("token")?.value;

  try {
    if (!token) {
      const refreshToken = request.cookies.get("refresh_token")?.value;
      if (!refreshToken) {
        throw new UnauthorizedError();
      }
      await submitTokenRefresh();
      return NextResponse.next();
    }
    await verifyToken(token);
    return NextResponse.next();
  }
  catch {
    return NextResponse.redirect(new URL("/login", request.url));
  }
}

export const config = {
  matcher: ["/dashboard/:path*", "/api/private/:path*"],
};
