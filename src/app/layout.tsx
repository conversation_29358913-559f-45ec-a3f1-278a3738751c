import "@/_ui/globals.css";
import { inter } from "@/_ui/fonts/fonts";

import { AppRouterCacheProvider } from "@mui/material-nextjs/v15-appRouter";

import { ThemeRegistry } from "@/_core/ui/components/themeRegistry/themeRegistry";
import { ReactQueryProvider } from "@/_core/lib/provider/tanstackQuery/components/reactQueryProvider";
import { Notifiable } from "@/_core/ui/components/notifiable/notifiable";

export const metadata = {
  title: {
    default: "FLCore NextJs",
    template: "%s | FLCore NextJs",
  },
  description: "FLCore Next - Core principal para desarrollos en NextJs",
  openGraph: {
    title: "FLCore Next - Core principal para desarrollos en NextJs",
  },
  twitter: {
    title: "FLCore Next - Core principal para desarrollos en NextJs",
  },
  icons: {
    icon: "../favicon.ico",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="es">
      <body className={`${inter.variable}`}>
        <AppRouterCacheProvider options={{ enableCssLayer: true }}>
          <ThemeRegistry>
            <ReactQueryProvider>
              <Notifiable>{children}</Notifiable>
            </ReactQueryProvider>
          </ThemeRegistry>
        </AppRouterCacheProvider>
      </body>
    </html>
  );
}
