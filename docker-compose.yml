version: '3.8'

services:
  fl-nextjs-core:
    build:
      context: .
      target: ${DOCKER_TARGET:-dev}
    container_name: ${SERVICE_NAME:-fl-nextjs-core}
    environment:
      - NODE_ENV=${NODE_ENV:-development}
      - NEXT_TELEMETRY_DISABLED=1
    volumes:
      # Only mount source code in development mode
      - ${DOCKER_VOLUMES:-./:/app:rw,cached}
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.${SERVICE_NAME:-fl-nextjs-core}.rule=Host(`${TRAEFIK_HOST:-fl-nextjs-core.local}`)"
      - "traefik.http.routers.${SERVICE_NAME:-fl-nextjs-core}.entrypoints=${TRAEFIK_ENTRYPOINT:-web}"
      - "traefik.http.services.${SERVICE_NAME:-fl-nextjs-core}.loadbalancer.server.port=3000"
      # Enable WebSocket and hot reload support
      - "traefik.http.services.${SERVICE_NAME:-fl-nextjs-core}.loadbalancer.passhostheader=true"
      - "traefik.http.services.${SERVICE_NAME:-fl-nextjs-core}.loadbalancer.sticky=false"
    restart: unless-stopped

networks:
  default:
    external:
      name: ${DOCKER_NETWORK:-devel}