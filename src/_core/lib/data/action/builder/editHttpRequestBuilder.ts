import { AxiosResponse } from "axios";

import { validateApiInput } from "@/_core/lib/service/validationService";

import { BaseHttpRequestBuilder } from "./baseHttpRequestBuilder";
import { BaseApiModel } from "@/_core/lib/data/interface/base/baseApiModel";

// Helper function to convert unknown values to query param types
function toQueryParams(data: Record<string, unknown>): Record<string, string | number | boolean> {
  const result: Record<string, string | number | boolean> = {};
  for (const [key, value] of Object.entries(data)) {
    if (typeof value === 'string' || typeof value === 'number' || typeof value === 'boolean') {
      result[key] = value;
    } else if (value !== null && value !== undefined) {
      result[key] = String(value);
    }
  }
  return result;
}

export class EditHttpRequestBuilder<TRequest = unknown, TResponse = unknown> extends BaseHttpRequestBuilder<
  TRequest,
  TResponse
> {
  constructor( url: string, method: "PUT" | "PATCH" = "PUT" ) {
    super(url, method);
  }

  withValidation(model: BaseApiModel, fullMask?: boolean): this {
    this.addBeforeExecute(() => {
      const resultParams = validateApiInput(model, "params", this["config"].params);
      if (fullMask) {
        this.setQueryParams(toQueryParams(resultParams));
      }

      const resultBody = validateApiInput(model, "body", this["config"].data);
      if (fullMask) {
        this.setBody(resultBody as TRequest);
      }
    });

    this.addAfterExecute((response: AxiosResponse<TResponse>) => {
      const result = validateApiInput(model, "response", response.data);
      if (fullMask) {
        response.data = result as TResponse;
      }
    });

    return this;
  }
}
