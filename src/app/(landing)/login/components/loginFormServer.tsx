"use server"
import { getSubmitActionFor } from "@/_lib/data/model/action/actionFactory";

import { metaLoginBody } from "../meta/metaLoginBody";
import { LoginFormClient } from "./loginFormClient";

const ENTITY = "token";
const ACTION = "login";

export async function LoginFormServer() {

  const serverAction = await getSubmitActionFor(ENTITY, ACTION);
  return (
    <LoginFormClient
      entityName={ENTITY}
      actionName={ACTION}
      meta={metaLoginBody}
      action={serverAction}
    />
  );
}
