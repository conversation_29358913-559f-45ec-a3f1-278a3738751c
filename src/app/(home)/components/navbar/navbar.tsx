import React, { ReactNode } from "react";
import Link from "next/link";
import RouteDispatcher from "./routeDispatcher";
import { RouteItem } from "./meta/types";
import Image from "next/image";
import { List, Box, Typography } from "@mui/material";

type NavbarProps = {
  routes?: RouteItem[];
  children?: ReactNode;
};

export function Navbar({ routes = [], children }: NavbarProps) {
  return (
    <Box
      className="
        flex flex-col
        h-full
        max-h-screen
        overflow-hidden
      "
    >
      {/* Encabezado fijo */}
      <Link
        href="/dashboard"
        className="
          flex flex-row items-center
          lg:pb-7 lg:pl-2
          shrink-0
          select-none
        "
      >
        <Box>
          <Image
            src="/bqn_logo.svg"
            alt="Logo"
            width={40}
            height={40}
            className="block mx-auto"
          />
        </Box>
        <Box className="flex flex-col justify-center lg:pl-3">
          <Typography
            sx={{ typography: { md: "h6xl" } }}
            className="font-bold"
            color="primary"
          >
            Bioquímicos NQN
          </Typography>
        </Box>
        {children}
      </Link>

      {/* Lista scrollable en hover */}
      <nav
        className="
          overflow-y-hidden
          hover:overflow-y-auto
          custom-scrollbar
          flex-1
        "
      >
        <List>
          {routes.map((item, i) => (
            <RouteDispatcher key={i} item={item} level={0} />
          ))}
        </List>
      </nav>
    </Box>
  );
}
