"use client";

import { useState } from "react";
import { useFormContext, FieldVal<PERSON>, Path } from "react-hook-form";

import { useTheme } from "@mui/material";

import { ErrorMessageRenderer } from "./errorMessageRenderer";

import {
  FormControl,
  InputLabel,
  OutlinedInput,
  InputAdornment,
  IconButton,
  Typography,
} from "@mui/material";

import { Eye, EyeOff, Lock } from "react-feather";
import { FieldMeta } from "../types";

export default function PasswordFieldRenderer<T extends FieldValues>({
  name,
  config,
}: {
  name: string;
  config: FieldMeta;
}) {
  const { register, formState } = useFormContext<T>();
  const error = formState.errors[name as Path<T>];
  const [show, setShow] = useState(false);

  const theme = useTheme();
  const iconColor = error
    ? theme.palette.error.main
    : theme.palette.text.secondary;

  return (
    <FormControl
      fullWidth
      variant={config.variant ?? "outlined"}
      size={config.size ?? "medium"}
      error={!!error}
    >
      <InputLabel
              htmlFor={name}
              sx={{
                "&.MuiInputLabel-shrink": {
                  transform: "translate(13px, -9px) scale(0.7)",
                },
              }}
            >
              <div className="flex items-center">
                <InputAdornment 
                  position="start"
                >
                  <Lock
                    size={config.size === "medium" ? 24 : 16}
                    color={iconColor}
                  />
                </InputAdornment>
                <Typography
                  className="align-top"
                  sx={{ typography: { xs: "body3", md: "body3xl", lg: "body2xl" } }}
                >
                  {config.label}
                </Typography>
              </div>
            </InputLabel>
      <OutlinedInput
        id={name}
        type={show ? "text" : "password"}
        label={
          <Typography
            className="align-top"
            sx={{ typography: { color: "textSecondary", xs: "body3", md: "body3xl", lg: "body2xl" } }}
          >
            {config.label}
          </Typography>
        }
        {...register(name as Path<T>)}
        endAdornment={
          <InputAdornment position="end">
            <IconButton onClick={() => setShow((s) => !s)} edge="end">
              {show ? (
                <Eye size={config.size === "medium" ? 24 : 16} />
              ) : (
                <EyeOff size={config.size === "medium" ? 24 : 16} />
              )}
            </IconButton>
          </InputAdornment>
        }
      />
      <ErrorMessageRenderer error={error} />
    </FormControl>
  );
}
