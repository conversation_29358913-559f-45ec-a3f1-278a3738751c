/**
 * Builder para endpoints de colección (GET /items).
 * Incluye paginación por defecto y permite configurar el tipo de datos.
 */
import { z, ZodTypeAny, ZodObject, ZodRawShape } from "zod";
import { ApiModelBaseBuilder } from "./ApiModelBaseBuilder";

export const paginationSchema = z
  .object({
    total_items: z.number().int().min(0).default(0).optional(),
    current_items: z.number().int().min(0).default(0).optional(),
    pages: z.number().int().min(1).default(1).optional(),
    current: z.number().int().min(1).default(1).optional(),
    limit: z.number().int().min(1).default(30).optional(),
  })
  .optional();

export const queryPaginationSchema = z.object({
  pagination: z.boolean().default(true).optional().nullable(),
  page: z.number().int().min(1).default(1).optional().nullable(),
  limit: z.number().int().min(1).max(30).default(30).optional().nullable(),
});

export class ApiModelCollectionBuilder extends ApiModelBaseBuilder {
  constructor(_name: string = "collection") {
    super(_name);
    this.setFilters(queryPaginationSchema);
    this.setResponse(
      z.object({
        items: z.array(z.unknown()),
        pagination: paginationSchema,
      })
    );
  }

  extendResponseSchema<T extends ZodRawShape>(schema: ZodObject<T>) {
    const oldResponse = this.schemas.response!;

    const merged = oldResponse ? oldResponse.merge(schema) : schema;

    this.setResponse(merged);
    return this;
  }

  extendFiltersSchema<T extends ZodRawShape>(schema: ZodObject<T>) {
    const oldFilters = this.schemas.filters!;

    const merged = oldFilters ? oldFilters.merge(schema) : schema;

    this.setFilters(merged);
    return this;
  }

  setResponseItemsSchema(schema: ZodTypeAny) {
    const oldResponse = this.schemas.response!;
    this.setResponse(
      oldResponse.extend({
        items: z.array(schema),
      })
    );
    return this;
  }

  setResponsePaginationSchema(schema: ZodTypeAny) {
    const oldResponse = this.schemas.response!;
    this.setResponse(
      oldResponse.extend({
        pagination: schema,
      })
    );
    return this;
  }
}
