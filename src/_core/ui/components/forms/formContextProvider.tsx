"use client";

import { ReactNode } from "react";
import {
  useForm,
  FormProvider,
  FieldValues,
  DefaultValues,
} from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { ZodTypeAny } from "zod";
import { useMutation } from "@tanstack/react-query";

export type MutationVariables = {
  formData: FormData;
  entityName: string;
  actionName: string;
};

type FormContextProviderProps<T extends FieldValues> = {
  schema: ZodTypeAny;
  children: ReactNode;
  defaultValues?: DefaultValues<T>;
  action: (formData: FormData) => Promise<unknown>;
  entityName: string;
  actionName: string;
};

export function FormContextProvider<T extends FieldValues>({
  schema,
  children,
  defaultValues,
  action,
  entityName,
  actionName,
}: FormContextProviderProps<T>) {
  const methods = useForm<T>({
    resolver: zodResolver(schema),
    defaultValues,
    mode: "onChange",
  });

  const mutation = useMutation<void, Error, MutationVariables>({
    mutationKey: [entityName, actionName],
    mutationFn: async ({ formData }) => {
      await action(formData);
    },
  });

  const onSubmit = async (values: T) => {
    const formData = new FormData();
    for (const [key, value] of Object.entries(values)) {
      formData.append(key, String(value));
    }
    await mutation.mutateAsync({ formData, entityName, actionName });
  };

  return (
    <FormProvider {...methods}>
      <form onSubmit={methods.handleSubmit(onSubmit)}>{children}</form>
    </FormProvider>
  );
}
