"use client";

import React from "react";
import ReadCollectionRenderer, { ColumnConfig } from "@/_core/ui/components/CRUD/readCollection/readCollectionRenderer";
import { renderers } from "@/_core/ui/components/CRUD/readCollection/renderers";
import { Typography } from "@mui/material";

const data = [
  {
    id: "1",
    profesional: "<PERSON>",
    matricula: "2525",
    localidad: "Aluminé",
    cuit: "27-15333457-5",
    tipo: "Activo",
    estado: "Activo",
    __actions: [
      { label: "Editar", icon: "edit", href: "/asociados/edit/1" },
      { label: "Eliminar", icon: "trash", href: "/asociados/delete/1" },
    ],
  },
  {
    id: "2",
    profesional: "<PERSON>",
    matricula: "2525",
    localidad: "Aluminé",
    cuit: "27-15333457-5",
    tipo: "Activo",
    estado: "Activo",
    __actions: [
      { label: "Editar", icon: "edit", href: "/asociados/edit/1" },
      { label: "Eliminar", icon: "trash", href: "/asociados/delete/1" },
    ],
  },
  {
    id: "3",
    profesional: "<PERSON> <PERSON>",
    matricula: "2525",
    localidad: "<PERSON>uminé",
    cuit: "27-15333457-5",
    tipo: "Activo",
    estado: "Activo",
    __actions: [
      { label: "Editar", icon: "edit", href: "/asociados/edit/1" },
      { label: "Eliminar", icon: "trash", href: "/asociados/delete/1" },
    ],
  },
];

const columnsConfig: ColumnConfig[] = [
  {
    key: "profesional",
    label: "PROFESIONAL",
    renderer: renderers.navigate,
    options: {
      basePath: "/asociados",
      paramKey: "id",
    },
  },
  { key: "matricula", label: "MATRICULA", renderer: renderers.integer },
  { key: "localidad", label: "LOCALIDAD", renderer: renderers.default },
  { key: "cuit", label: "CUIT", renderer: renderers.default },
  { key: "tipo", label: "TIPO", renderer: renderers.default },
  {
    key: "estado",
    label: "ESTADO",
    renderer: renderers.badge,
    options: {
      bg: "bg-gray-100",
      text: "text-gray-700",
    },
  },
  { key: "__actions", label: "", renderer: renderers.actions },
];

export default function AsociadosPage() {
  return (
    <main>
      <Typography
        sx={{ typography: { xs: "h6", md: "h6xl", lg: "h2xl" } }}
        className="pl-4"
        color="text.primary"
      >
        Asociados
      </Typography>
      <ReadCollectionRenderer data={data} columnsConfig={columnsConfig} />
    </main>
  );
}
