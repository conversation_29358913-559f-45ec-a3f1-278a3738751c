"server-only";

import { cookies } from "next/headers";
import { redirect } from "next/navigation";

import { ENTITIES } from "@/_lib/data/model/entities";

import { validateApiInput } from "@/_core/lib/service/validationService";
import { verifyToken } from "@/_core/lib/context/auth/authTokenService";

import { BaseHttpRequestBuilder } from "@/_core/lib/data/action/builder";
import { TokenInterface } from "@/_lib/data/model/interface";

import { UnauthorizedError } from "@/_core/lib/context/error/badRequestError";

const { token } = ENTITIES;

export async function submitTokenLogin(body: FormData) {
  "use server";

  const { login: { meta } } = token;

  const cookiesStore = await cookies();
  const ObjectBody = Object.fromEntries(body);

  const builder = new BaseHttpRequestBuilder( meta.url, meta.method )
    .setBody(ObjectBody)
    .addBeforeBuild(() => {
      const result = validateApiInput(
        TokenInterface.tokenModel,
        "body",
        ObjectBody
      );
      builder.setBody(result);
    })
    .addAfterExecute((response) => {
      const result = validateApiInput(
        TokenInterface.tokenModel,
        "response",
        response.data
      );
      response.data = result;
    })
    .addAfterExecute(async (response) => {
      const data = response.data as { token: string; refreshToken: string };
      const payload = await verifyToken(data.token);
      cookiesStore.set("token", data.token, {
        httpOnly: true,
        sameSite: "lax",
        path: "/",
        expires: payload.exp
          ? new Date(payload.exp * 1000)
          : new Date(Date.now() + 1000 * 60 * 5),
      });
      cookiesStore.set("refreshToken", data.refreshToken, {
        httpOnly: true,
        sameSite: "lax",
        path: "/",
        expires: new Date(Date.now() + 1000 * 60 * 60 * 24 * 5),
      });
    })
    .addAfterExecute(() => {
      redirect("/dashboard");
    });

  const response = await builder.run();
  return response.data;
}

export async function submitTokenRefresh() {
  "use server";

  const { refresh: { meta } } = token;

  const cookiesStore = await cookies();
  const refreshToken = cookiesStore.get("refreshToken")?.value;

  if (!refreshToken) {
    throw new UnauthorizedError();
  }

  const builder = new BaseHttpRequestBuilder( meta.url, meta.method )
    .setBody({ refreshToken: refreshToken })
    .addBeforeBuild(() => {
      const result = validateApiInput(
        TokenInterface.refreshTokenModel,
        "body",
        { refreshToken: refreshToken }
      );
      builder.setBody(result);
    })
    .addAfterExecute((response) => {
      const result = validateApiInput(
        TokenInterface.refreshTokenModel,
        "response",
        response.data
      );
      response.data = result;
    })
    .addAfterExecute(async (response) => {
      const data = response.data as { token: string; refreshToken: string };
      const payload = await verifyToken(data.token);
      cookiesStore.set("token", data.token, {
        httpOnly: true,
        sameSite: "lax",
        path: "/",
        expires: payload.exp
          ? new Date(payload.exp * 1000)
          : new Date(Date.now() + 1000 * 60 * 5),
      });
    });

  const response = await builder.run();
  return response.data;
}

export async function submitTokenLogout() {
  "use server";

  // const { logout: { meta } } = token;

  const cookiesStore = await cookies();
  cookiesStore.delete("token");
  cookiesStore.delete("refreshToken");
  redirect("/login");
}

export async function submitTokenRecovery(body: FormData) {
  "use server";

  const { recovery: { meta } } = token;

  const ObjectBody = Object.fromEntries(body);

  const builder = new BaseHttpRequestBuilder( meta.url, meta.method )
    .setBody(ObjectBody)
    .addBeforeBuild(() => {
      const result = validateApiInput(
        TokenInterface.recoveryTokenModel,
        "body",
        ObjectBody
      );
      builder.setBody(result);
    })
    .addAfterExecute((response) => {
      const result = validateApiInput(
        TokenInterface.recoveryTokenModel,
        "response",
        response.data
      );
      response.data = result;
    });

  const response = await builder.run();
  return response.data;
}
