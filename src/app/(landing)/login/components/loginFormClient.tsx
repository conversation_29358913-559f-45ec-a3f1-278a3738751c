"use client";

import Link from "next/link";

import { ClientFormWrapper } from "@/_core/ui/components/forms/clientFormWrapper";
import { FieldMeta } from "@/_core/ui/components/forms/types";
import { InferSchema, EntityKeys, ActionKeys } from "@/_lib/data/model/schema";

import { Box, Checkbox, FormControlLabel, Typography } from "@mui/material";
import { FormSubmitButton } from "@/_core/ui/components/forms/formSubmitButton";

type LoginFormClientProps<E extends EntityKeys, A extends ActionKeys<E>> = {
  entityName: E;
  actionName: A;
  meta: Record<keyof InferSchema<E, A>, FieldMeta>;
  action: (formData: FormData) => Promise<unknown>;
};

export function LoginFormClient<E extends EntityKeys, A extends ActionKeys<E>>({
  entityName,
  actionName,
  meta,
  action,
}: LoginFormClientProps<E, A>) {
  return (
    <Box className="mt-6 md:mt-10 lg:mt-12 md:mx-2 lg:mx-0">
      <ClientFormWrapper
        entityName={entityName}
        actionName={actionName}
        meta={meta}
        action={action}
      >
        <Box
          className="
            grid
            lg:grid-cols-7
            items-center 
            md:mt-1 lg:mt-5
            mx-1 lg:mx-0"
        >
          <Box className="order-1 mt-2 md:mt-2 lg:mt-3 lg:col-span-4 md:col-span-7">
            <FormControlLabel
              control={<Checkbox size="small" />}
              label={
                <Typography
                  sx={{ typography: { xs: "body3", md: "body3xl" } }}
                  color="textSecondary"
                >
                  Recordar este dispositivo
                </Typography>
              }
            />
          </Box>
          <Box className="lg:order-2 order-3 col-span-7 lg:col-span-3 justify-self-center mt-2 md:mt-3 lg:mt-3">
            <Link href="/recovery">
                <Typography
                  className="hover:underline"
                  sx={{ typography: { xs: "body2", md: "body3xl" } }}
                  color="link"
                >
                  ¿Olvidaste tu contraseña?
                </Typography>
            </Link>
          </Box>
          <Box className="lg:order-3 order-2 col-span-7 md:mt-3 lg:mt-5">
            <div className="py-2">
              <FormSubmitButton label="Ingresar" />
            </div>
          </Box>
        </Box>
      </ClientFormWrapper>
    </Box>
  );
}
