import { z } from "zod";
import { ENTITIES } from "../entities";

export const ENTITY = ENTITIES.token;

export const tokenSchemas = {
  login: {
    body: z.object({
      username: z
        .string()
        .min(3, "Ingrese por los menos 3 caracteres")
        .max(20, "No puede superar los 20 caracteres"),
      password: z
        .string()
        .min(3, "Ingrese por los menos 3 caracteres")
        .max(20, "No puede superar los 20 caracteres"),
    }),
    response: z.object({
      token: z.string(),
      refreshToken: z.string().optional().nullable(),
    }),
  },
  refresh: {
    body: z.object({
      refreshToken: z.string(),
    }),
    response: z.object({
      token: z.string(),
      refreshToken: z.string().optional().nullable(),
    }),
  },
  recovery: {
    body: z.object({
      username: z
        .string()
        .min(3, "Ingrese por los menos 3 caracteres")
        .max(20, "No puede superar los 20 caracteres"),
    }),
    response: z.object({
      username: z.string().optional().nullable(),
      email: z.string(),
    }),
  }
} as const;

// Tipos inferidos (opcional, si querés usarlos explícitamente)
export type TokenBody = z.infer<typeof tokenSchemas.login.body>;
export type TokenResponse = z.infer<typeof tokenSchemas.login.response>;
export type RefreshTokenBody = z.infer<typeof tokenSchemas.refresh.body>;
export type RefreshTokenResponse = z.infer<
  typeof tokenSchemas.refresh.response
>;
