import React from "react";
import Link from "next/link";
import RowActionsMenu from "@/_core/ui/components/rowActionsMenu/rowActionsMenu";
import { IconKey } from "@/_ui/icons/iconMap";
import { Badge, Typography } from "@mui/material";

// Tipo para representar una fila de datos genérica
export type DataRow = Record<string, unknown>;

// Tipo para opciones de renderizado
export type RendererOptions = Record<string, unknown>;

export type ActionMeta = {
  label: string;
  icon: IconKey;
  href: string | ((row: DataRow) => string);
};

export type ColumnRenderer = (
  value: unknown,
  row: DataRow,
  columnName: string,
  options?: RendererOptions
) => React.ReactNode;

// Función helper para convertir unknown a string de forma segura
const toString = (value: unknown): string => {
  if (value === null || value === undefined) return '';
  return String(value);
};

// Función helper para convertir unknown a number de forma segura
const toNumber = (value: unknown): number => {
  const num = Number(value);
  return isNaN(num) ? 0 : num;
};

export const renderers: Record<string, ColumnRenderer> = {
  link: (value) => {
    const href = toString(value);
    return (
      <a
        href={href}
        className="text-blue-600 underline"
        target="_blank"
        rel="noopener noreferrer"
      >
          <Typography color="link.main" className="hover:underline">
              {href}
          </Typography>
      </a>
    );
  },

  money: (value) => {
    const amount = toNumber(value);
    return <Typography color="text.main">${amount.toFixed(2)}</Typography>;
  },

  integer: (value) => {
    const num = toNumber(value);
    return <Typography color="text.main">{Math.floor(num)}</Typography>;
  },

  actions: (_, row) => {
    const actions = row.__actions as ActionMeta[] | undefined;
    return <RowActionsMenu row={row} actions={actions || []} />;
  },

  navigate: (value, row, _, options) => {
    const basePath = toString(options?.basePath) || "";
    const paramKey = toString(options?.paramKey) || "id";
    const paramValue = toString(row[paramKey]);
    const href = `${basePath}/${paramValue}`;
    const displayValue = toString(value);

    return (
      <Link href={href}>
        <Typography color="link.main" className="hover:underline">
            {displayValue}
        </Typography>
      </Link>
    );
  },

  badge: (value, _row, _col, options) => {
    const bg = toString(options?.bg) || "bg-gray-100";
    const text = toString(options?.text) || "text-gray-800";
    const displayValue = toString(value);

    return (
      <Badge className={`px-2 py-1 rounded-full ${bg} ${text}`}>
        <Typography color="text.main">{displayValue}</Typography>
      </Badge>
    );
  },

  hidden: () => null, // No se renderiza nada (ni en cabeza ni cuerpo)

  default: (value) => {
    const displayValue = toString(value);
    return <Typography color="text.main">{displayValue}</Typography>;
  },
};
