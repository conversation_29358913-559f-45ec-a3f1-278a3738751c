"use client";

import { useEffect, useRef, useState } from "react";
import { queryClient } from "@/_core/lib/provider/tanstackQuery/config/queryClient";
import { MutationVariables } from "@/_core/ui/components/forms/formContextProvider";

import { BackdropLoader } from "../backdropLoader/backdropLoader";
import { Toaster } from "@/_core/ui/components/toaster/sonner";
import { showErrorToast } from "@/_core/ui/components/toaster/toast";

export function Notifiable({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const [isMutating, setIsMutating] = useState(false);
  const activeMutationsCount = useRef(0);

  useEffect(() => {
    /**
     * Suscripción a eventos de query de tanstack-query
     */
    const unsubscribeQueryEvents = queryClient
      .getQueryCache()
      .subscribe((event) => {
        const { query } = event;
        const { status, data, error } = query.state;

        if (event.type === "updated") {
          if (status === "success") {
            console.log("Query Success", query.queryKey, data);
          }

          if (status === "error") {
            console.log("Query Error", query.queryKey, error);
          }
        }
      });

    /**
     * Suscripción a eventos de mutación de tanstack-query
     */
    const unsubscribeMutationEvents = queryClient
      .getMutationCache()
      .subscribe((event) => {
        const mutation = event.mutation;
        if (!mutation) return;

        const { status, error } = mutation.state as {
          status: string;
          error?: any;
          variables?: MutationVariables;
        };

        if (event.type === "added") {
          activeMutationsCount.current += 1;
          setIsMutating(true);
        }

        if (
          event.type === "updated" &&
          (status === "success" || status === "error")
        ) {
          activeMutationsCount.current = Math.max(
            activeMutationsCount.current - 1,
            0
          );

          if (status === "error") {
            const message =
              error?.response?.data?.message ||
              error?.message ||
              "Error inesperado en la operación";
            if (error?.message !== "NEXT_REDIRECT"){
              showErrorToast(message);
            }
          }

          if (activeMutationsCount.current === 0) {
            setIsMutating(false);
          }
        }
      });

    return () => {
      unsubscribeQueryEvents();
      unsubscribeMutationEvents();
    };
  }, []);

  return (
    <>
      {children}

      <BackdropLoader open={isMutating} />

      <Toaster />
    </>
  );
}
