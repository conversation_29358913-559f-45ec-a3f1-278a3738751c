/**
 * Rendering utilities for CRUD operations
 * Provides common rendering functions and helpers
 */

import React from "react";
import Link from "next/link";
import { Badge, Typography } from "@mui/material";
import { toString, toNumber } from '../typeConversion';
import { DataRow, RendererOptions, ActionMeta } from './dataTypes';

/**
 * Default renderers for common data types
 */
export const defaultRenderers = {
  /**
   * Renders a clickable link
   */
  link: (value: unknown) => {
    const href = toString(value);
    return (
      <a
        href={href}
        className="text-blue-600 underline"
        target="_blank"
        rel="noopener noreferrer"
      >
        <Typography color="link.main" className="hover:underline">
          {href}
        </Typography>
      </a>
    );
  },

  /**
   * Renders a monetary value
   */
  money: (value: unknown) => {
    const amount = toNumber(value);
    return <Typography color="text.main">${amount.toFixed(2)}</Typography>;
  },

  /**
   * Renders an integer value
   */
  integer: (value: unknown) => {
    const num = toNumber(value);
    return <Typography color="text.main">{Math.floor(num)}</Typography>;
  },

  /**
   * Renders a navigation link
   */
  navigate: (value: unknown, row: DataRow, _: string, options?: RendererOptions) => {
    const basePath = toString(options?.basePath) || "";
    const paramKey = toString(options?.paramKey) || "id";
    const paramValue = toString(row[paramKey]);
    const href = `${basePath}/${paramValue}`;
    const displayValue = toString(value);

    return (
      <Link href={href}>
        <Typography color="link.main" className="hover:underline">
          {displayValue}
        </Typography>
      </Link>
    );
  },

  /**
   * Renders a badge
   */
  badge: (value: unknown, _row: DataRow, _col: string, options?: RendererOptions) => {
    const bg = toString(options?.bg) || "bg-gray-100";
    const text = toString(options?.text) || "text-gray-800";
    const displayValue = toString(value);

    return (
      <Badge className={`px-2 py-1 rounded-full ${bg} ${text}`}>
        <Typography color="text.main">{displayValue}</Typography>
      </Badge>
    );
  },

  /**
   * Hidden renderer (returns null)
   */
  hidden: () => null,

  /**
   * Default text renderer
   */
  default: (value: unknown) => {
    const displayValue = toString(value);
    return <Typography color="text.main">{displayValue}</Typography>;
  },
} as const;

/**
 * Creates a custom renderer for boolean values
 * @param trueText - Text to display for true values
 * @param falseText - Text to display for false values
 * @param trueColor - Color for true values
 * @param falseColor - Color for false values
 */
export function createBooleanRenderer(
  trueText: string = "Yes",
  falseText: string = "No",
  trueColor: string = "success.main",
  falseColor: string = "error.main"
) {
  return (value: unknown) => {
    const boolValue = Boolean(value);
    return (
      <Typography color={boolValue ? trueColor : falseColor}>
        {boolValue ? trueText : falseText}
      </Typography>
    );
  };
}

/**
 * Creates a custom renderer for date values
 * @param format - Date format options
 */
export function createDateRenderer(format?: Intl.DateTimeFormatOptions) {
  return (value: unknown) => {
    const date = new Date(toString(value));
    if (isNaN(date.getTime())) {
      return <Typography color="text.main">-</Typography>;
    }
    
    const formatted = date.toLocaleDateString(undefined, format);
    return <Typography color="text.main">{formatted}</Typography>;
  };
}

/**
 * Creates a custom renderer for truncated text
 * @param maxLength - Maximum length before truncation
 * @param suffix - Suffix to add when truncated
 */
export function createTruncateRenderer(maxLength: number = 50, suffix: string = "...") {
  return (value: unknown) => {
    const text = toString(value);
    const truncated = text.length > maxLength ? text.substring(0, maxLength) + suffix : text;
    
    return (
      <Typography color="text.main" title={text}>
        {truncated}
      </Typography>
    );
  };
}

/**
 * Creates a custom renderer for status badges with predefined colors
 * @param statusColors - Map of status values to colors
 */
export function createStatusRenderer(statusColors: Record<string, string>) {
  return (value: unknown) => {
    const status = toString(value).toLowerCase();
    const color = statusColors[status] || "bg-gray-100 text-gray-800";
    
    return (
      <Badge className={`px-2 py-1 rounded-full ${color}`}>
        <Typography color="text.main">{toString(value)}</Typography>
      </Badge>
    );
  };
}

/**
 * Safely renders a value with fallback
 * @param value - Value to render
 * @param fallback - Fallback content if value is null/undefined
 */
export function safeRender(value: unknown, fallback: React.ReactNode = "-"): React.ReactNode {
  if (value === null || value === undefined || value === "") {
    return fallback;
  }
  return toString(value);
}
