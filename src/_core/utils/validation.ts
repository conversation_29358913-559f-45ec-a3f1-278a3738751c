/**
 * Validation utilities
 * Provides common validation functions and helpers
 */

import { BaseApiModel, SchemaKind } from "@/_core/lib/data/interface/base/baseApiModel";
import { SchemaValidationError } from "@/_core/lib/context/error/badRequestError";
import { toQueryParams } from './typeConversion';

/**
 * Validates API input using a model schema
 * @param model - API model with schemas
 * @param kind - Schema kind to validate against
 * @param input - Input data to validate
 * @returns Validated and parsed data
 * @throws SchemaValidationError if validation fails
 */
export function validateApiInput(
  model: BaseApiModel, 
  kind: SchemaKind, 
  input: unknown
): Record<string, unknown> {
  const schema = model.getSchema(kind);
  const result = schema.validate(input);
  
  if (!result.success) {
    const err = result.error;
    const firstErrorName = err.issues[0].path[0].toString();
    const firstError = err.issues[0].message;
    
    throw new SchemaValidationError(firstError, { atribute_name: firstErrorName });
  }
  
  return result.data as Record<string, unknown>;
}

/**
 * Validates API input and converts to query parameters
 * @param model - API model with schemas
 * @param kind - Schema kind to validate against
 * @param input - Input data to validate
 * @returns Validated data as query parameters
 */
export function validateAndConvertToQueryParams(
  model: BaseApiModel,
  kind: SchemaKind,
  input: unknown
): Record<string, string | number | boolean> {
  const validated = validateApiInput(model, kind, input);
  return toQueryParams(validated);
}

/**
 * Email validation regex
 */
const EMAIL_REGEX = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

/**
 * URL validation regex
 */
const URL_REGEX = /^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$/;

/**
 * UUID validation regex
 */
const UUID_REGEX = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;

/**
 * Validates an email address
 * @param email - Email to validate
 * @returns True if valid email
 */
export function isValidEmail(email: string): boolean {
  return EMAIL_REGEX.test(email);
}

/**
 * Validates a URL
 * @param url - URL to validate
 * @returns True if valid URL
 */
export function isValidUrl(url: string): boolean {
  return URL_REGEX.test(url);
}

/**
 * Validates a UUID
 * @param uuid - UUID to validate
 * @returns True if valid UUID
 */
export function isValidUuid(uuid: string): boolean {
  return UUID_REGEX.test(uuid);
}

/**
 * Validates a phone number (basic validation)
 * @param phone - Phone number to validate
 * @returns True if valid phone number
 */
export function isValidPhone(phone: string): boolean {
  const cleaned = phone.replace(/\D/g, '');
  return cleaned.length >= 10 && cleaned.length <= 15;
}

/**
 * Validates a password strength
 * @param password - Password to validate
 * @param minLength - Minimum length (default: 8)
 * @param requireUppercase - Require uppercase letter (default: true)
 * @param requireLowercase - Require lowercase letter (default: true)
 * @param requireNumbers - Require numbers (default: true)
 * @param requireSpecialChars - Require special characters (default: true)
 * @returns Validation result with details
 */
export function validatePassword(
  password: string,
  options: {
    minLength?: number;
    requireUppercase?: boolean;
    requireLowercase?: boolean;
    requireNumbers?: boolean;
    requireSpecialChars?: boolean;
  } = {}
): {
  isValid: boolean;
  errors: string[];
  strength: 'weak' | 'medium' | 'strong';
} {
  const {
    minLength = 8,
    requireUppercase = true,
    requireLowercase = true,
    requireNumbers = true,
    requireSpecialChars = true,
  } = options;

  const errors: string[] = [];
  let score = 0;

  if (password.length < minLength) {
    errors.push(`Password must be at least ${minLength} characters long`);
  } else {
    score += 1;
  }

  if (requireUppercase && !/[A-Z]/.test(password)) {
    errors.push('Password must contain at least one uppercase letter');
  } else if (/[A-Z]/.test(password)) {
    score += 1;
  }

  if (requireLowercase && !/[a-z]/.test(password)) {
    errors.push('Password must contain at least one lowercase letter');
  } else if (/[a-z]/.test(password)) {
    score += 1;
  }

  if (requireNumbers && !/\d/.test(password)) {
    errors.push('Password must contain at least one number');
  } else if (/\d/.test(password)) {
    score += 1;
  }

  if (requireSpecialChars && !/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
    errors.push('Password must contain at least one special character');
  } else if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
    score += 1;
  }

  // Additional strength checks
  if (password.length >= 12) score += 1;
  if (/[!@#$%^&*(),.?":{}|<>].*[!@#$%^&*(),.?":{}|<>]/.test(password)) score += 1;

  let strength: 'weak' | 'medium' | 'strong';
  if (score <= 2) strength = 'weak';
  else if (score <= 4) strength = 'medium';
  else strength = 'strong';

  return {
    isValid: errors.length === 0,
    errors,
    strength,
  };
}

/**
 * Validates a credit card number using Luhn algorithm
 * @param cardNumber - Credit card number to validate
 * @returns True if valid credit card number
 */
export function isValidCreditCard(cardNumber: string): boolean {
  const cleaned = cardNumber.replace(/\D/g, '');
  
  if (cleaned.length < 13 || cleaned.length > 19) {
    return false;
  }

  let sum = 0;
  let isEven = false;

  for (let i = cleaned.length - 1; i >= 0; i--) {
    let digit = parseInt(cleaned[i], 10);

    if (isEven) {
      digit *= 2;
      if (digit > 9) {
        digit -= 9;
      }
    }

    sum += digit;
    isEven = !isEven;
  }

  return sum % 10 === 0;
}

/**
 * Validates a date string
 * @param dateString - Date string to validate
 * @param format - Expected format (optional)
 * @returns True if valid date
 */
export function isValidDate(dateString: string, format?: string): boolean {
  const date = new Date(dateString);
  
  if (isNaN(date.getTime())) {
    return false;
  }

  // If format is specified, validate against it
  if (format) {
    // This is a simplified format validation
    // In a real application, you might want to use a library like date-fns
    const formatRegex = format
      .replace(/YYYY/g, '\\d{4}')
      .replace(/MM/g, '\\d{2}')
      .replace(/DD/g, '\\d{2}');
    
    return new RegExp(`^${formatRegex}$`).test(dateString);
  }

  return true;
}

/**
 * Validates that a value is within a numeric range
 * @param value - Value to validate
 * @param min - Minimum value (inclusive)
 * @param max - Maximum value (inclusive)
 * @returns True if value is within range
 */
export function isInRange(value: number, min: number, max: number): boolean {
  return value >= min && value <= max;
}

/**
 * Validates that a string has a specific length range
 * @param str - String to validate
 * @param minLength - Minimum length
 * @param maxLength - Maximum length
 * @returns True if string length is within range
 */
export function isValidLength(str: string, minLength: number, maxLength: number): boolean {
  return str.length >= minLength && str.length <= maxLength;
}

/**
 * Validates that an array has a specific length range
 * @param arr - Array to validate
 * @param minLength - Minimum length
 * @param maxLength - Maximum length
 * @returns True if array length is within range
 */
export function isValidArrayLength<T>(arr: T[], minLength: number, maxLength: number): boolean {
  return arr.length >= minLength && arr.length <= maxLength;
}
