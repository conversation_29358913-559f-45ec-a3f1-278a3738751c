/**
 * Data transformation utilities
 * Provides functions for transforming data between different formats and structures
 */

import { toQueryParams } from './typeConversion';

/**
 * Transforms form data to a plain object
 * @param formData - FormData instance
 * @returns Plain object representation
 */
export function formDataToObject(formData: FormData): Record<string, unknown> {
  const result: Record<string, unknown> = {};
  
  for (const [key, value] of formData.entries()) {
    if (key in result) {
      // Handle multiple values for the same key
      const existing = result[key];
      if (Array.isArray(existing)) {
        existing.push(value);
      } else {
        result[key] = [existing, value];
      }
    } else {
      result[key] = value;
    }
  }
  
  return result;
}

/**
 * Transforms an object to FormData
 * @param obj - Object to transform
 * @returns FormData instance
 */
export function objectToFormData(obj: Record<string, unknown>): FormData {
  const formData = new FormData();
  
  for (const [key, value] of Object.entries(obj)) {
    if (value === null || value === undefined) {
      continue;
    }
    
    if (Array.isArray(value)) {
      value.forEach(item => {
        formData.append(key, String(item));
      });
    } else if (value instanceof File) {
      formData.append(key, value);
    } else {
      formData.append(key, String(value));
    }
  }
  
  return formData;
}

/**
 * Flattens a nested object into a flat structure
 * @param obj - Object to flatten
 * @param prefix - Prefix for keys (used internally for recursion)
 * @param separator - Separator for nested keys
 * @returns Flattened object
 */
export function flattenObject(
  obj: Record<string, unknown>, 
  prefix: string = '', 
  separator: string = '.'
): Record<string, unknown> {
  const result: Record<string, unknown> = {};
  
  for (const [key, value] of Object.entries(obj)) {
    const newKey = prefix ? `${prefix}${separator}${key}` : key;
    
    if (value !== null && typeof value === 'object' && !Array.isArray(value) && !(value instanceof Date)) {
      Object.assign(result, flattenObject(value as Record<string, unknown>, newKey, separator));
    } else {
      result[newKey] = value;
    }
  }
  
  return result;
}

/**
 * Unflattens a flat object into a nested structure
 * @param obj - Flat object to unflatten
 * @param separator - Separator used in keys
 * @returns Nested object
 */
export function unflattenObject(
  obj: Record<string, unknown>, 
  separator: string = '.'
): Record<string, unknown> {
  const result: Record<string, unknown> = {};
  
  for (const [key, value] of Object.entries(obj)) {
    const keys = key.split(separator);
    let current = result;
    
    for (let i = 0; i < keys.length - 1; i++) {
      const k = keys[i];
      if (!(k in current) || typeof current[k] !== 'object' || Array.isArray(current[k])) {
        current[k] = {};
      }
      current = current[k] as Record<string, unknown>;
    }
    
    current[keys[keys.length - 1]] = value;
  }
  
  return result;
}

/**
 * Deep clones an object
 * @param obj - Object to clone
 * @returns Deep cloned object
 */
export function deepClone<T>(obj: T): T {
  if (obj === null || typeof obj !== 'object') {
    return obj;
  }
  
  if (obj instanceof Date) {
    return new Date(obj.getTime()) as T;
  }
  
  if (Array.isArray(obj)) {
    return obj.map(item => deepClone(item)) as T;
  }
  
  const cloned = {} as T;
  for (const key in obj) {
    if (Object.prototype.hasOwnProperty.call(obj, key)) {
      cloned[key] = deepClone(obj[key]);
    }
  }
  
  return cloned;
}

/**
 * Merges multiple objects deeply
 * @param target - Target object
 * @param sources - Source objects to merge
 * @returns Merged object
 */
export function deepMerge<T extends Record<string, unknown>>(target: T, ...sources: Partial<T>[]): T {
  if (!sources.length) return target;
  
  const source = sources.shift();
  if (!source) return target;
  
  for (const key in source) {
    if (Object.prototype.hasOwnProperty.call(source, key)) {
      const sourceValue = source[key];
      const targetValue = target[key];
      
      if (sourceValue !== null && typeof sourceValue === 'object' && !Array.isArray(sourceValue)) {
        if (targetValue !== null && typeof targetValue === 'object' && !Array.isArray(targetValue)) {
          target[key] = deepMerge(targetValue as Record<string, unknown>, sourceValue as Record<string, unknown>) as T[Extract<keyof T, string>];
        } else {
          target[key] = deepClone(sourceValue) as T[Extract<keyof T, string>];
        }
      } else {
        target[key] = sourceValue as T[Extract<keyof T, string>];
      }
    }
  }
  
  return deepMerge(target, ...sources);
}

/**
 * Picks specific keys from an object
 * @param obj - Source object
 * @param keys - Keys to pick
 * @returns Object with only the specified keys
 */
export function pick<T extends Record<string, unknown>, K extends keyof T>(
  obj: T, 
  keys: K[]
): Pick<T, K> {
  const result = {} as Pick<T, K>;
  
  for (const key of keys) {
    if (key in obj) {
      result[key] = obj[key];
    }
  }
  
  return result;
}

/**
 * Omits specific keys from an object
 * @param obj - Source object
 * @param keys - Keys to omit
 * @returns Object without the specified keys
 */
export function omit<T extends Record<string, unknown>, K extends keyof T>(
  obj: T, 
  keys: K[]
): Omit<T, K> {
  const result = { ...obj };
  
  for (const key of keys) {
    delete result[key];
  }
  
  return result;
}

/**
 * Transforms object keys using a transformation function
 * @param obj - Source object
 * @param transform - Function to transform keys
 * @returns Object with transformed keys
 */
export function transformKeys<T extends Record<string, unknown>>(
  obj: T,
  transform: (key: string) => string
): Record<string, T[keyof T]> {
  const result: Record<string, T[keyof T]> = {};
  
  for (const [key, value] of Object.entries(obj)) {
    result[transform(key)] = value;
  }
  
  return result;
}

/**
 * Converts camelCase keys to snake_case
 * @param obj - Object with camelCase keys
 * @returns Object with snake_case keys
 */
export function camelToSnake<T extends Record<string, unknown>>(obj: T): Record<string, T[keyof T]> {
  return transformKeys(obj, key => 
    key.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`)
  );
}

/**
 * Converts snake_case keys to camelCase
 * @param obj - Object with snake_case keys
 * @returns Object with camelCase keys
 */
export function snakeToCamel<T extends Record<string, unknown>>(obj: T): Record<string, T[keyof T]> {
  return transformKeys(obj, key => 
    key.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase())
  );
}

// Re-export toQueryParams for convenience
export { toQueryParams };
