"use client";

import Link from "next/link";
import {
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
} from "@mui/material";
import { RouteLink } from "../meta/types";
import { iconMap } from "@/_ui/icons/iconMap";

type Props = {
  item: RouteLink;
  level?: number;
};

export default function LinkItem({ item }: Props) {
  const Icon = item.icon ? iconMap[item.icon] : null;

  return (
    <ListItem>
      <ListItemButton disableGutters component={Link} href={item.href}>
        {Icon && (
          <>
            <ListItemIcon>
              <Icon size={24} />
            </ListItemIcon>
            <ListItemText primary={item.label} />
          </>
        )}
        {!Icon && (
          <ListItemText
            className="pl-2"
            primary={
              <>
                <span className="pr-3" >{"•"}</span>
                {item.label}
              </>
            }
          />
        )}
      </ListItemButton>
    </ListItem>
  );
}
