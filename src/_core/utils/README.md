# Core Utils

Esta carpeta contiene utilidades centralizadas para evitar duplicación de código en la aplicación.

## Estructura

```
src/_core/utils/
├── index.ts                 # Barrel export principal
├── typeConversion.ts        # Utilidades de conversión de tipos
├── crud/                    # Utilidades específicas para CRUD
│   ├── index.ts            # Barrel export de CRUD
│   ├── dataTypes.ts        # Tipos comunes para CRUD
│   └── renderers.tsx       # Renderers reutilizables
└── README.md               # Esta documentación
```

## Utilidades Disponibles

### Type Conversion (`typeConversion.ts`)

Funciones para convertir valores `unknown` a tipos específicos de forma segura:

- `toString(value, fallback?)` - Convierte a string
- `toNumber(value, fallback?)` - Convierte a number
- `toQueryParams(data)` - Convierte objeto a query parameters

### CRUD Utils (`crud/`)

#### Tipos (`dataTypes.ts`)
- `DataRow` - Tipo para filas de datos genéricas
- `RendererOptions` - Opciones para renderers
- `ActionMeta` - Metadatos para acciones de fila
- `ColumnRenderer` - Función de renderizado de columnas
- `ColumnConfig` - Configuración de columnas

#### Renderers (`renderers.tsx`)
Renderers predefinidos para tipos comunes de datos:
- `link` - Enlaces clickeables
- `money` - Valores monetarios
- `integer` - Números enteros
- `actions` - Menú de acciones
- `navigate` - Enlaces de navegación
- `badge` - Badges/etiquetas
- `hidden` - Columnas ocultas
- `default` - Renderer por defecto

## Uso

### Importación desde el barrel export principal:
```typescript
import { toString, toNumber, toQueryParams } from '@/_core/utils';
import { DataRow, renderers } from '@/_core/utils';
```

### Importación específica:
```typescript
import { toString } from '@/_core/utils/typeConversion';
import { DataRow, renderers } from '@/_core/utils/crud';
```

## Migración

Los archivos existentes han sido refactorizados para usar estas utilidades centralizadas:

- `src/_core/ui/components/CRUD/readCollection/renderers.tsx` - Ahora re-exporta desde utils
- `src/_core/ui/components/rowActionsMenu/rowActionsMenu.tsx` - Usa tipos centralizados
- Builders HTTP - Usan `toQueryParams` centralizada

## Beneficios

1. **DRY (Don't Repeat Yourself)** - Elimina código duplicado
2. **Consistencia** - Comportamiento uniforme en toda la app
3. **Mantenibilidad** - Cambios centralizados
4. **Reutilización** - Fácil uso en nuevos componentes
5. **Testing** - Pruebas centralizadas de utilidades comunes
