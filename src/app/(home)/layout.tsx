"use server";

import OneTabOnlyGuard from "@/_core/ui/components/oneTabOnlyGuard/oneTabOnlyGuard";
import { Navbar } from "./components/navbar/navbar";
import { routes } from "./components/navbar/meta/routes";

import { Avatar, Box, Paper } from "@mui/material";

export default async function HomeLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <Box
      className="flex min-h-screen overflow-x-hidden"
      sx={{ bgcolor: "background.default" }}
    >
      <OneTabOnlyGuard />

      {/* Aside izquierdo - ocupa toda la altura */}
      <aside
        className="
        hidden lg:block sticky top-0
        h-screen max-h-screen
        p-7 pl-6 pr-0
        2xl:w-90 xl:w-74 lg:w-58
        border-r-1
        border-gray-200"
      >
        <Navbar routes={routes}></Navbar>
      </aside>

      {/* Contenido principal: Header + Main + Right Aside */}
      <div className="flex flex-col flex-1">
        <header className="sticky top-0">
          <Paper
            elevation={10}
            className="h-18 px-12 flex items-center justify-end"
            sx={{
              boxShadow: "0px 0px 4px rgba(0, 0, 0, 0.1)",
              bgcolor: "background.paper", // asegúrate que tenga fondo
            }}
          >
            <Avatar sx={{ bgcolor: "secondary.main" }}/>
          </Paper>
        </header>

        <div className="flex flex-1 flex-col 2xl:flex-row">
          <main className="flex-1 py-8 pl-4 pr-8 h-[calc(100vh-4.5rem)] max-h-[calc(100vh-4.5rem)] overflow-y-auto custom-scrollbar">
            {children}
          </main>
        </div>
      </div>
    </Box>
  );
}
