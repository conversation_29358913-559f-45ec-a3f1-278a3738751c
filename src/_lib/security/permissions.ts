"server-only";
import { PermissionNotFoundError } from "@/_core/lib/context/error/internalServerError";
import { ENTITIES } from "@/_lib/data/model/entities";

// Entidades válidas extraídas de ENTITIES
export type EntityKeys = keyof typeof ENTITIES;

// Acciones válidas por cada entidad
export type ActionKeys<E extends EntityKeys> = keyof (typeof ENTITIES)[E];

// Tipos de contextos
export type Context =
  | "public"
  | "colegio"
  | "asociado"
  | "laboratorio"
  | "sucursal"
  | "obra_social"
  | "admin";

// Contextos válidos por entidad y acción
type PermissionsFromEntities = {
  [E in keyof typeof ENTITIES]: {
    [A in keyof (typeof ENTITIES)[E]]: Context[];
  };
};

// Permisos estrictamente tipados: cada entidad → acciones válidas → contextos permitidos
export const permissions: PermissionsFromEntities = {
  token: {
    login: ["public"],
    refresh: ["public"],
    logout: ["public"],
    recovery: ["public"],
  },

  /** Otros permisos acá */
} satisfies PermissionsFromEntities;

// Función segura: solo acepta entidades y acciones válidas
export function hasAccess<E extends EntityKeys, A extends ActionKeys<E>>(
  entity: E,
  action: A,
  context?: Context
): boolean {
  const allowedContexts = permissions?.[entity]?.[action];

  if (!allowedContexts) {
    throw new PermissionNotFoundError(entity, action as string);
  }

  return allowedContexts.includes(context ?? "public") || context === "admin";
}
