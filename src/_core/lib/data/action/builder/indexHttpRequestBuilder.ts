import { AxiosResponse } from "axios";
import { validateApiInput } from "@/_core/lib/service/validationService";
import { BaseHttpRequestBuilder } from "./baseHttpRequestBuilder";
import { BaseApiModel } from "@/_core/lib/data/interface/base/baseApiModel";
import { toQueryParams } from "@/_core/utils/typeConversion";

export class IndexHttpRequestBuilder<TRequest = unknown, TResponse = unknown> extends BaseHttpRequestBuilder<
  TRequest,
  TResponse
> {
  constructor(url: string) {
    super(url, "GET");
  }

  withValidation(model: BaseApiModel, fullMask?: boolean): this {
    this.addBeforeExecute(() => {
      const result = validateApiInput(model, "params", this["config"].params);
      if (fullMask) {
        this.setQueryParams(toQueryParams(result));
      }
    });

    this.addAfterExecute((response: AxiosResponse<TResponse>) => {
      const result = validateApiInput(model, "response", response.data);
      if (fullMask) {
        response.data = result as TResponse;
      }
    });

    return this;
  }
}
