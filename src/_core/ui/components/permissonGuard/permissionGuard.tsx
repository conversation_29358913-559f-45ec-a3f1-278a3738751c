import { cookies } from "next/headers";
import { hasAccess, Context, Entity<PERSON>eys, ActionKeys } from "@/_lib/security/permissions";
import { ForbiddenError } from "@/_core/lib/context/error/badRequestError";

type PermissionGuardProps<E extends EntityKeys> = {
  entity: E;
  action: ActionKeys<E>;
  children: React.ReactNode;
  fallback?: React.ReactNode;
};

// Componente server-side que muestra su contenido solo si hay permiso
export async function PermissionGuard<E extends EntityKeys>({
  entity,
  action,
  children,
  fallback,
}: PermissionGuardProps<E>) {
  const cookieStore = await cookies();
  const context = cookieStore.get("context")?.value as Context | undefined;

  const canAccess = hasAccess(entity, action, context);

  if (canAccess) return <>{children}</>;

  if (fallback) return <>{fallback}</>;

  throw new ForbiddenError();
}
