"use client";

import React from "react";
import { ListSubheader, List, Divider } from "@mui/material";
import RouteDispatcher from "../routeDispatcher";
import { RouteHeader } from "../meta/types";

type Props = {
  item: RouteHeader;
};

export default function HeaderItem({ item }: Props) {
  return (
    <List
      subheader={
        <>
          <ListSubheader
            className={
              "uppercase font-semibold pointer-events-none select-none"
            }
          >
            <Divider />
            {item.label}
          </ListSubheader>
        </>
      }
    >
      {item.childs?.map((child, idx) => (
        <RouteDispatcher key={idx} item={child} />
      ))}
    </List>
  );
}
