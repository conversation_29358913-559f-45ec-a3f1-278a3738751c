/**
 * Type conversion utilities for safely converting unknown values to specific types
 * These functions provide safe type conversion with fallback values
 */

/**
 * Safely converts unknown value to string
 * @param value - The value to convert
 * @param fallback - Fallback value if conversion fails (default: empty string)
 * @returns String representation of the value
 */
export function toString(value: unknown, fallback: string = ''): string {
  if (value === null || value === undefined) return fallback;
  return String(value);
}

/**
 * Safely converts unknown value to number
 * @param value - The value to convert
 * @param fallback - Fallback value if conversion fails (default: 0)
 * @returns Numeric representation of the value
 */
export function toNumber(value: unknown, fallback: number = 0): number {
  const num = Number(value);
  return isNaN(num) ? fallback : num;
}

/**
 * Safely converts unknown value to integer
 * @param value - The value to convert
 * @param fallback - Fallback value if conversion fails (default: 0)
 * @returns Integer representation of the value
 */
export function toInteger(value: unknown, fallback: number = 0): number {
  const num = toNumber(value, fallback);
  return Math.floor(num);
}

/**
 * Safely converts unknown value to boolean
 * @param value - The value to convert
 * @param fallback - Fallback value if conversion fails (default: false)
 * @returns Boolean representation of the value
 */
export function toBoolean(value: unknown, fallback: boolean = false): boolean {
  if (value === null || value === undefined) return fallback;
  if (typeof value === 'boolean') return value;
  if (typeof value === 'string') {
    const lower = value.toLowerCase();
    return lower === 'true' || lower === '1' || lower === 'yes';
  }
  if (typeof value === 'number') return value !== 0;
  return fallback;
}

/**
 * Safely converts unknown value to Date
 * @param value - The value to convert
 * @param fallback - Fallback value if conversion fails (default: current date)
 * @returns Date representation of the value
 */
export function toDate(value: unknown, fallback?: Date): Date {
  if (value instanceof Date) return value;
  if (typeof value === 'string' || typeof value === 'number') {
    const date = new Date(value);
    return isNaN(date.getTime()) ? (fallback || new Date()) : date;
  }
  return fallback || new Date();
}

/**
 * Safely converts unknown value to array
 * @param value - The value to convert
 * @param fallback - Fallback value if conversion fails (default: empty array)
 * @returns Array representation of the value
 */
export function toArray<T = unknown>(value: unknown, fallback: T[] = []): T[] {
  if (Array.isArray(value)) return value as T[];
  if (value === null || value === undefined) return fallback;
  return [value as T];
}

/**
 * Type guard to check if value is not null or undefined
 * @param value - The value to check
 * @returns True if value is not null or undefined
 */
export function isDefined<T>(value: T | null | undefined): value is T {
  return value !== null && value !== undefined;
}

/**
 * Type guard to check if value is a string
 * @param value - The value to check
 * @returns True if value is a string
 */
export function isString(value: unknown): value is string {
  return typeof value === 'string';
}

/**
 * Type guard to check if value is a number
 * @param value - The value to check
 * @returns True if value is a number and not NaN
 */
export function isNumber(value: unknown): value is number {
  return typeof value === 'number' && !isNaN(value);
}

/**
 * Type guard to check if value is a boolean
 * @param value - The value to check
 * @returns True if value is a boolean
 */
export function isBoolean(value: unknown): value is boolean {
  return typeof value === 'boolean';
}

/**
 * Converts query parameters from Record<string, unknown> to Record<string, string | number | boolean>
 * @param data - The data to convert
 * @returns Converted query parameters
 */
export function toQueryParams(data: Record<string, unknown>): Record<string, string | number | boolean> {
  const result: Record<string, string | number | boolean> = {};
  for (const [key, value] of Object.entries(data)) {
    if (typeof value === 'string' || typeof value === 'number' || typeof value === 'boolean') {
      result[key] = value;
    } else if (isDefined(value)) {
      result[key] = toString(value);
    }
  }
  return result;
}
