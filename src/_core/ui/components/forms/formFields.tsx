"use client";

import React from "react";
import dynamic from "next/dynamic";

import { FieldMeta, FieldType } from "./types";

import { useMemo } from "react";
import { useTheme, useMediaQuery } from "@mui/material";
import { FieldValues } from "react-hook-form";

import { Box } from "@mui/material";

const PasswordFieldRenderer = dynamic(
  () => import("./fields/passwordFieldRenderer")
);
const TextFieldRenderer = dynamic(() => import("./fields/textFieldRenderer"));
const CheckboxFieldRenderer = dynamic(
  () => import("./fields/checkboxFieldRenderer")
);
const UsernameFieldRenderer = dynamic(
  () => import("./fields/usernameFieldRenderer")
);

const fieldRendererMap: Record<
  FieldType,
  (props: { name: string; config: FieldMeta }) => React.ReactElement
> = {
  text: (props) => <TextFieldRenderer {...props} />,
  email: (props) => <TextFieldRenderer {...props} />,
  number: (props) => <TextFieldRenderer {...props} />,
  checkbox: (props) => <CheckboxFieldRenderer {...props} />,
  password: (props) => <PasswordFieldRenderer {...props} />,
  username: (props) => <UsernameFieldRenderer {...props} />,
};

type FormFieldsProps<T extends FieldValues> = {
  meta: Record<keyof T & string, FieldMeta>;
};

export function FormFields<T extends FieldValues>({
  meta,
}: FormFieldsProps<T>) {
  const theme = useTheme();
  const isLgUp = useMediaQuery(theme.breakpoints.up("lg"));

  // Tamaño por defecto para los campos en funcion del breakpoint actual
  const defaultSize = isLgUp ? "medium" : "small";

  // Agregamos los valores por defecto a los metadatos
  const defaultsAddedMeta = useMemo(() => {
    return Object.entries(meta).reduce((acc, [name, config]) => {
      acc[name] = { ...config, size: config.size ?? defaultSize };
      return acc;
    }, {} as Record<string, FieldMeta>);
  }, [meta, defaultSize]);

  // Renderizamos los campos
  return (
    <Box sx={{ display: "flex", flexDirection: "column", gap: { xs: 1, md: 2 } }}>
      {Object.entries(defaultsAddedMeta).map(([name, config]) => {
        config.size = config.size ?? defaultSize;
        const Renderer = fieldRendererMap[config.type];
        return <Renderer key={name} name={name} config={config} />;
      })}
    </Box>
  );
}
