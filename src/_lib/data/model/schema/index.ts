import { z, ZodType } from "zod";

// Importar todos los módulos de esquemas
import { tokenSchemas } from "./token";

// Mapeo de entidad → módulo de esquemas
const schemaModules = {
  token: tokenSchemas,
};

// Tipos de clave válidos
export type SchemaType = "body" | "query" | "params" | "headers" | "response";

// Tipo de una acción: cada una puede tener uno o más Zod schemas (body, response, etc.)
// eslint-disable-next-line @typescript-eslint/no-explicit-any
type ActionSchema = Partial<Record<SchemaType, ZodType<any, any, any>>>;
type EntitySchema = Record<string, ActionSchema>;

// Generación automática del objeto `schemas`
export const schemas: Record<string, EntitySchema> = Object.entries(schemaModules).reduce(
  (acc, [entityKey, actions]) => {
    const actionSchemas: EntitySchema = Object.entries(actions).reduce(
      (actionAcc, [actionName, schemaMap]) => {
        actionAcc[actionName] = schemaMap as ActionSchema;
        return actionAcc;
      },
      {} as EntitySchema
    );
    acc[entityKey] = actionSchemas;
    return acc;
  },
  {} as Record<string, EntitySchema>
);

// ---------------------------
// Tipado utilitario
// ---------------------------

// Nivel 1: entidades
export type EntityKeys = keyof typeof schemas;

// Nivel 2: acciones dentro de una entidad
export type ActionKeys<E extends EntityKeys> = keyof (typeof schemas)[E];

// Nivel 3: tipos de esquema dentro de una acción
export type SchemaTypeKeys<
  E extends EntityKeys,
  A extends ActionKeys<E>
> = Extract<keyof (typeof schemas)[E][A], SchemaType>;

// Inferencia automática de tipos de esquema
export type InferSchema<
  E extends EntityKeys,
  A extends ActionKeys<E>,
  S extends SchemaTypeKeys<E, A> = "body"
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
> = (typeof schemas)[E][A][S] extends ZodType<any, any, any>
  ? z.infer<(typeof schemas)[E][A][S]>
  : never;
