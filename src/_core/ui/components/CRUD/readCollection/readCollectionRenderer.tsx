"use client";

import React from "react";
import {
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
  Pagination,
} from "@mui/material";
import { renderers } from "./renderers";
import { ColumnRenderer, DataRow } from "@/_core/utils/crud";

export type ColumnConfig = {
  key: string;
  label?: string;
  renderer?: ColumnRenderer;
  options?: Record<string, unknown>;
};

type Props = {
  data: DataRow[];
  columnsConfig?: ColumnConfig[];
};

export default function ReadCollectionRenderer({ data, columnsConfig }: Props) {
  if (!data || data.length === 0) {
    return <div className="p-4">No hay datos para mostrar</div>;
  }

  const columns: ColumnConfig[] = columnsConfig
    ? columnsConfig
    : Object.keys(data[0])
        .filter((col) => col !== "__actions")
        .map((key) => ({ key, renderer: renderers.default }));

  // Para saber si una columna es oculta (renderer === hidden)
  const isVisible = (col: ColumnConfig) => col.renderer !== renderers.hidden;

  return (
    <Paper elevation={3} className="mt-7 p-8">
      <TableContainer>
        <Table sx={{ border: "2px solid #F2F4F8" }}>
          <TableHead sx={{ bgcolor: "primary.main" }}>
            <TableRow>
              {columns.filter(isVisible).map(({ key, label }) => (
                <TableCell key={key} sx={{ color: "primary.contrastText" }}>
                  <Typography
                    className="font-bold"
                    sx={{
                      typography: { xs: "body3", md: "body3xl", lg: "body2xl" },
                    }}
                  >
                    {label ?? key}
                  </Typography>
                </TableCell>
              ))}
            </TableRow>
          </TableHead>
          <TableBody>
            {data.map((row, index) => (
              <TableRow key={index} sx={{ borderBottom: "2px solid #F2F4F8" }}>
                {columns.filter(isVisible).map(({ key, renderer, options }) => (
                  <TableCell key={key}>
                    {renderer
                      ? renderer(row[key], row, key, options)
                      : String(row[key] ?? '')}
                  </TableCell>
                ))}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
      <Pagination
        count={10}
        color="primary"
        className="flex justify-end mt-4"
      ></Pagination>
    </Paper>
  );
}
