import { DefaultError, DefaultErrorOptions } from "../default";

export interface schemaValidationErrorOptions extends DefaultErrorOptions {
  atribute_name: string;
}

export class SchemaValidationError extends DefaultError {
  public readonly atribute_name: string;

  constructor( message: string, options: schemaValidationErrorOptions) {
    super(`${message}`, {
      code: options.code || "400_01_SCHEMA_VALIDATION",
      statusCode: options.statusCode || 400,
      details: options.details,
    });
    
    this.name = "SchemaValidationError";
    this.atribute_name = options.atribute_name;

    // Mantener el stack trace.
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, this.constructor);
    }
  }
}
