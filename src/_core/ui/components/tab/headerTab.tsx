"use client";

import { Chip, Paper, Typography } from "@mui/material";
import { ReactNode } from "react";

type Badge = {
  label: string;
  bgColor: string;
  textColor: string;
};

type Props = {
  title: string;
  subtitle?: string;
  badges?: Badge[];
  actions?: ReactNode;
  children?: ReactNode;
};

export default function HeaderTab({
  title,
  subtitle,
  badges = [],
  actions,
  children,
}: Props) {
  return (
    <Paper className="p-6 space-y-4">
      <div className="flex justify-between items-start">
        <div>
          <div className="flex items-center space-x-2">
            <Typography
              sx={{ typography: { xs: "h5", md: "h5xl", lg: "h4xl" }, color: "primary.main" }}
            >
              {title}
            </Typography>
            <div className="flex ml-6 space-x-2">
              {badges.map(({ label, bgColor, textColor }) => (
                <Chip
                  key={label}
                  label={
                    <Typography
                      sx={{ typography: { xs: "body2", md: "body1xl", lg: "body1xl" }, color: textColor }}
                    >
                      {label}
                    </Typography>
                  }
                  sx={{ backgroundColor: bgColor }}
                  size="small"
                />
              ))}
            </div>
          </div>
          {subtitle && (
            <Typography
              className="mt-4"
              sx={{ typography: { xs: "body2", md: "body2xl", lg: "body1xl" } }}
              color="text.secondary"
            >
              {subtitle}
            </Typography>
          )}
        </div>
        {actions}
      </div>
      {children}
    </Paper>
  );
}
