"use client";

import { useForm<PERSON>ontext, <PERSON>V<PERSON><PERSON>, Path } from "react-hook-form";
import { FieldMeta } from "../types";

import { ErrorMessageRenderer } from "./errorMessageRenderer";

import { FormControl, TextField, Typography } from "@mui/material";

export default function TextFieldRenderer<T extends FieldValues>({
  name,
  config,
}: {
  name: string;
  config: FieldMeta;
}) {
  const { register, formState } = useFormContext<T>();
  const error = formState.errors[name as Path<T>];

  return (
    <FormControl>
      <TextField
        fullWidth
        type={config.type}
        label={
          <Typography
            className="align-top"
            sx={{ typography: { xs: "body3", md: "body3xl", lg: "body2xl" } }}
          >
            {config.label}
          </Typography>
        }
        variant={config.variant ?? "outlined"}
        size={config.size ?? "medium"}
        {...register(name as Path<T>)}
        error={!!error}
      />
      <ErrorMessageRenderer error={error} />
    </FormControl>
  );
}
