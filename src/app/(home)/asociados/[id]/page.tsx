"use client";
import ReadIndexRenderer from "@/_core/ui/components/CRUD/readIndex/readIndexRenderer";
import EstadoUpdaterButton from "@/_core/ui/components/button/estadoUpdaterButton";
import HistoricalAccordion from "@/_core/ui/components/acordion/historicalAcordion";
import { Breadcrumbs, Typography } from "@mui/material";
import Link from "next/link";
import EditableTab from "@/_core/ui/components/tab/editableTab";

export default function AsociadoPage() {
  return (
    <div className="p-6 space-y-4">
      {/* Breadcrumbs */}
      <Breadcrumbs aria-label="breadcrumb">
        <Link
          className="hover:underline"
          href="/dashboard"
          color="inherit"
        >
          Home
        </Link>
        <Link
          className="hover:underline"
          href="/asociados"
          color="inherit"
        >
          Asociados
        </Link>
        <Typography color="inherit" className="pointer-events-none select-none"><PERSON></Typography>
      </Breadcrumbs>

      {/* Detalle del asociado */}
      <ReadIndexRenderer
        title="<PERSON>"
        subtitle="DNI: 15333457"
        badges={[
          {
            label: "Activo",
            bgColor: "#F2F4F8",
            textColor: "contrastText.main",
          },
        ]}
        actions={<EstadoUpdaterButton onEstadoUpdate={() => {}} />}
        tabs={[
          {
            label: "Datos personales",
            content: (
              <EditableTab>
                <div className="text-sm space-y-2">
                  <Typography variant="body1" color="text.secondary">
                    <strong>NOMBRE Y APELLIDO:</strong> Juan Luis Hernandez
                  </Typography>
                  <Typography variant="body1" color="text.secondary">
                    <strong>FECHA NACIMIENTO:</strong> dd/mm/aaaa
                  </Typography>
                  <Typography variant="body1" color="text.secondary">
                    <strong>DNI:</strong> 15333457
                  </Typography>
                  <Typography variant="body1" color="text.secondary">
                    <strong>CUIT:</strong> 27-15333457-5
                  </Typography>
                  <Typography variant="body1" color="text.secondary">
                    <strong>FECHA DE ALTA:</strong> dd/mm/aaaa
                  </Typography>

                  <HistoricalAccordion
                    title="Historial"
                    items={[
                      { label: "dd/mm/aaaa", value: "BAJA" },
                      { label: "MOTIVO:", value: "Fallecimiento" },
                      { label: "dd/mm/aaaa", value: "ACTIVO" },
                    ]}
                  />
                </div>
              </EditableTab>
            ),
          },
          {
            label: "Laboral",
            content: <div>Datos laborales</div>,
          },
          {
            label: "Cuenta",
            content: <div>Datos de cuenta</div>,
          },
          {
            label: "Configuraciones",
            content: <div>Opciones</div>,
          },
          {
            label: "Trámites",
            content: <div>Historial de trámites</div>,
          },
        ]}
      />
    </div>
  );
}
