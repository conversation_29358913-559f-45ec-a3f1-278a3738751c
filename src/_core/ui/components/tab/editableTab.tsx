"use client";

import { Card } from "@mui/material";
import EditButton from "@/_core/ui/components/button/editButton";
import { ReactNode } from "react";

type Props = {
  onEdit?: () => void;
  children: ReactNode;
};

export default function EditableTab({ onEdit = () => {}, children }: Props) {
  return (
    <Card className="p-6 relative">
      <div className="absolute top-4 right-4">
        <EditButton onEdit={onEdit} />
      </div>
      {children}
    </Card>
  );
}
