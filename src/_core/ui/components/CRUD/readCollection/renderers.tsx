import React from "react";
import Link from "next/link";
import RowActionsMenu from "@/_core/ui/components/rowActionsMenu/rowActionsMenu";
import { IconKey } from "@/_ui/icons/iconMap";
import { Badge, Typography } from "@mui/material";

export type ActionMeta = {
  label: string;
  icon: IconKey;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  href: string | ((row: Record<string, any>) => string);
};

export type ColumnRenderer = (
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  value: any,
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  row: Record<string, any>,
  columnName: string,
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  options?: Record<string, any>
) => React.ReactNode;

export const renderers: Record<string, ColumnRenderer> = {
  link: (value) => (
    <a
      href={value}
      className="text-blue-600 underline"
      target="_blank"
      rel="noopener noreferrer"
    >
        <Typography color="link.main" className="hover:underline">
            {value}
        </Typography>
    </a>
  ),

  money: (value) => <Typography color="text.main">${Number(value).toFixed(2)}</Typography>,

  integer: (value) => <Typography color="text.main">{parseInt(value, 10)}</Typography>,

  actions: (_, row) => <RowActionsMenu row={row} actions={row.__actions} />,

  navigate: (value, row, _, options) => {
    const basePath = options?.basePath ?? "";
    const paramKey = options?.paramKey ?? "id";
    const href = `${basePath}/${row[paramKey]}`;

    return (
      <Link
        href={href}
      >
        <Typography color="link.main" className="hover:underline">
            {value}
        </Typography>
      </Link>
    );
  },

  badge: (value, _row, _col, options) => {
    const bg = options?.bg ?? "bg-gray-100";
    const text = options?.text ?? "text-gray-800";

    return (
      <Badge className={`px-2 py-1 rounded-full ${bg} ${text}`}>
        <Typography color="text.main">{value}</Typography>
      </Badge>
    );
  },

  hidden: () => null, // No se renderiza nada (ni en cabeza ni cuerpo)

  default: (value) => <Typography color="text.main">{value}</Typography>,
};
