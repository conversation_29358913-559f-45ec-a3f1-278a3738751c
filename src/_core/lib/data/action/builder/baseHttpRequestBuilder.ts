import { AxiosResponse, isAxiosError, Method, ResponseType } from "axios";
import {
  BaseHttpRequest,
  StrictHttpActionParams,
} from "../base/baseHttpRequest";
import { httpConfig } from "@/_lib/config/httpConfig";

import {
  Hook,
  AfterBuildHook,
  AfterExecuteHook,
  HttpRequestBuilderHooks,
} from "../hooks/httpRequestBuilderHooks";

import { FetchingError } from "@/_core/lib/context/error";

/**
 * Builder base para construir y ejecutar una BaseHttpRequest con Axios.
 * permite múltiples hooks por etapa.
 */
export class BaseHttpRequestBuilder<TRequest = unknown, TResponse = unknown> {
  protected config: Partial<StrictHttpActionParams<TRequest>> = {};
  protected hooks = new HttpRequestBuilderHooks<TRequest, TResponse>();

  constructor( url: string, method: Method) {
    this.setBaseUrl(httpConfig.baseURL)
      .setTimeout(httpConfig.timeout)
      .setResponseType("json")
      .setHeaders({ "Content-Type": "application/json" });
    this.setUrl(url);
    this.setMethod(method);
  }

  // Registro de hooks encadenables
  addBeforeBuild(hook: Hook): this {
    this.hooks.addBeforeBuild(hook);
    return this;
  }

  addAfterBuild(hook: AfterBuildHook<TRequest, TResponse>): this {
    this.hooks.addAfterBuild(hook);
    return this;
  }

  addBeforeExecute(hook: Hook): this {
    this.hooks.addBeforeExecute(hook);
    return this;
  }

  addAfterExecute(hook: AfterExecuteHook<TResponse>): this {
    this.hooks.addAfterExecute(hook);
    return this;
  }

  // Configuración de request
  setMethod(method: Method): this {
    this.config.method = method;
    return this;
  }

  setUrl(url: string): this {
    this.config.url = url;
    return this;
  }

  setHeaders(headers: Record<string, string>): this {
    this.config.headers = headers;
    return this;
  }

  addHeader(headers: Record<string, string>): this {
    this.config.headers = this.config.headers || {};
    this.config.headers = { ...this.config.headers, ...headers };
    return this;
  }

  setQueryParams(params: Record<string, unknown>): this {
    this.config.params = params;
    return this;
  }

  setBody(data: TRequest): this {
    this.config.data = data;
    return this;
  }

  setTimeout(timeout: number): this {
    this.config.timeout = timeout;
    return this;
  }

  setResponseType(type: ResponseType): this {
    this.config.responseType = type;
    return this;
  }

  setAuth(username: string, password: string): this {
    this.config.auth = { username, password };
    return this;
  }

  setBaseUrl(baseURL: string): this {
    this.config.baseURL = baseURL;
    return this;
  }

  setWithCredentials(value: boolean): this {
    this.config.withCredentials = value;
    return this;
  }

  /**
   * Construcción + ejecución
   */
  async run(): Promise<AxiosResponse<TResponse>> {
    try {
      await this.hooks.runBeforeBuild();

      const request = this.build();

      await this.hooks.runAfterBuild(request);
      await this.hooks.runBeforeExecute();

      const response = await request.execute();
      await this.hooks.runAfterExecute(response);
      return response;
    } catch (error) {
      if (isAxiosError(error)) {
        throw new FetchingError(error);
      }
      throw error;
    }
  }

  /**
   * Construye la request base.
   */
  protected build(): BaseHttpRequest<TRequest, TResponse> {
    if (!this.config.url || !this.config.method) {
      throw new Error("Faltan parámetros obligatorios: 'url' y 'method'");
    }

    return new BaseHttpRequest<TRequest, TResponse>(
      this.config as StrictHttpActionParams<TRequest>
    );
  }
}
