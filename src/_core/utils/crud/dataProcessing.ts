/**
 * Data processing utilities for CRUD operations
 * Provides functions for data transformation, filtering, sorting, and pagination
 */

import { DataRow, PaginationConfig, SortConfig, FilterConfig } from './dataTypes';
import { toString, toNumber, toBoolean } from '../typeConversion';

/**
 * Sorts data by a specific field and direction
 * @param data - Array of data rows
 * @param sortConfig - Sort configuration
 * @returns Sorted array of data rows
 */
export function sortData(data: DataRow[], sortConfig: SortConfig): DataRow[] {
  if (!sortConfig.field) return data;

  return [...data].sort((a, b) => {
    const aValue = a[sortConfig.field];
    const bValue = b[sortConfig.field];

    // Handle null/undefined values
    if (aValue === null || aValue === undefined) return 1;
    if (bValue === null || bValue === undefined) return -1;

    // Convert to comparable types
    const aStr = toString(aValue).toLowerCase();
    const bStr = toString(bValue).toLowerCase();
    const aNum = toNumber(aValue);
    const bNum = toNumber(bValue);

    let comparison = 0;

    // Try numeric comparison first
    if (!isNaN(aNum) && !isNaN(bNum)) {
      comparison = aNum - bNum;
    } else {
      // Fall back to string comparison
      comparison = aStr.localeCompare(bStr);
    }

    return sortConfig.direction === 'desc' ? -comparison : comparison;
  });
}

/**
 * Filters data based on filter configuration
 * @param data - Array of data rows
 * @param filters - Filter configuration
 * @returns Filtered array of data rows
 */
export function filterData(data: DataRow[], filters: FilterConfig): DataRow[] {
  if (!filters || Object.keys(filters).length === 0) return data;

  return data.filter(row => {
    return Object.entries(filters).every(([field, filterValue]) => {
      const rowValue = row[field];
      
      if (filterValue === null || filterValue === undefined || filterValue === '') {
        return true; // No filter applied
      }

      // Handle different filter types
      if (typeof filterValue === 'string') {
        return toString(rowValue).toLowerCase().includes(filterValue.toLowerCase());
      }

      if (typeof filterValue === 'number') {
        return toNumber(rowValue) === filterValue;
      }

      if (typeof filterValue === 'boolean') {
        return toBoolean(rowValue) === filterValue;
      }

      // Handle array filters (multiple values)
      if (Array.isArray(filterValue)) {
        return filterValue.some(value => 
          toString(rowValue).toLowerCase().includes(toString(value).toLowerCase())
        );
      }

      // Handle object filters (range, etc.)
      if (typeof filterValue === 'object' && filterValue !== null) {
        const filter = filterValue as Record<string, unknown>;
        
        // Range filter
        if ('min' in filter || 'max' in filter) {
          const numValue = toNumber(rowValue);
          const min = filter.min ? toNumber(filter.min) : -Infinity;
          const max = filter.max ? toNumber(filter.max) : Infinity;
          return numValue >= min && numValue <= max;
        }

        // Date range filter
        if ('startDate' in filter || 'endDate' in filter) {
          const dateValue = new Date(toString(rowValue));
          const startDate = filter.startDate ? new Date(toString(filter.startDate)) : new Date(0);
          const endDate = filter.endDate ? new Date(toString(filter.endDate)) : new Date();
          return dateValue >= startDate && dateValue <= endDate;
        }
      }

      return toString(rowValue) === toString(filterValue);
    });
  });
}

/**
 * Paginates data based on pagination configuration
 * @param data - Array of data rows
 * @param paginationConfig - Pagination configuration
 * @returns Paginated data and updated pagination info
 */
export function paginateData(
  data: DataRow[], 
  paginationConfig: PaginationConfig
): { data: DataRow[]; pagination: PaginationConfig } {
  const { page, limit } = paginationConfig;
  const total = data.length;
  const totalPages = Math.ceil(total / limit);
  const startIndex = (page - 1) * limit;
  const endIndex = startIndex + limit;

  return {
    data: data.slice(startIndex, endIndex),
    pagination: {
      ...paginationConfig,
      total,
      totalPages,
    },
  };
}

/**
 * Processes data with sorting, filtering, and pagination
 * @param data - Array of data rows
 * @param sortConfig - Sort configuration
 * @param filters - Filter configuration
 * @param paginationConfig - Pagination configuration
 * @returns Processed data and metadata
 */
export function processData(
  data: DataRow[],
  sortConfig?: SortConfig,
  filters?: FilterConfig,
  paginationConfig?: PaginationConfig
): {
  data: DataRow[];
  pagination?: PaginationConfig;
  totalBeforePagination: number;
} {
  let processedData = [...data];

  // Apply filters first
  if (filters) {
    processedData = filterData(processedData, filters);
  }

  // Then apply sorting
  if (sortConfig) {
    processedData = sortData(processedData, sortConfig);
  }

  const totalBeforePagination = processedData.length;

  // Finally apply pagination
  let pagination: PaginationConfig | undefined;
  if (paginationConfig) {
    const result = paginateData(processedData, paginationConfig);
    processedData = result.data;
    pagination = result.pagination;
  }

  return {
    data: processedData,
    pagination,
    totalBeforePagination,
  };
}

/**
 * Validates data structure
 * @param data - Array of data rows
 * @returns Array of validation errors (empty if valid)
 */
export function validateData(data: DataRow[]): string[] {
  const errors: string[] = [];

  if (!Array.isArray(data)) {
    errors.push('Data must be an array');
    return errors;
  }

  if (data.length === 0) {
    return errors; // Empty data is valid
  }

  // Check for consistent structure
  const firstRowKeys = Object.keys(data[0]).sort();
  
  for (let i = 1; i < data.length; i++) {
    const currentRowKeys = Object.keys(data[i]).sort();
    
    if (JSON.stringify(firstRowKeys) !== JSON.stringify(currentRowKeys)) {
      errors.push(`Row ${i} has inconsistent structure compared to first row`);
    }
  }

  return errors;
}

/**
 * Transforms data for export (CSV, Excel, etc.)
 * @param data - Array of data rows
 * @param columns - Column keys to include (if not provided, all columns are included)
 * @returns Transformed data suitable for export
 */
export function transformForExport(
  data: DataRow[], 
  columns?: string[]
): Record<string, string>[] {
  if (data.length === 0) return [];

  const columnsToInclude = columns || Object.keys(data[0]);

  return data.map(row => {
    const transformedRow: Record<string, string> = {};
    
    columnsToInclude.forEach(column => {
      transformedRow[column] = toString(row[column]);
    });

    return transformedRow;
  });
}
