// src/theme.ts
import { createTheme } from '@mui/material/styles';
import type { ThemeOptions } from '@mui/material/styles';

// Extensiones de tipos para h7 y breakpoint '2xl'
declare module '@mui/material/styles' {

  interface Palette {
    link: Palette['primary'];
  }

  interface PaletteOptions {
    link?: PaletteOptions['primary'];
  }

  interface TypographyVariants {
    h1xl: React.CSSProperties;
    h2xl: React.CSSProperties;
    h3xl: React.CSSProperties;
    h4xl: React.CSSProperties;
    h5xl: React.CSSProperties;
    h6xl: React.CSSProperties;
    body1xl: React.CSSProperties;
    body2xl: React.CSSProperties;
    body3xl: React.CSSProperties;
    body3: React.CSSProperties;
  }

  interface TypographyVariantsOptions {
    h1xl?: React.CSSProperties;
    h2xl?: React.CSSProperties;
    h3xl?: React.CSSProperties;
    h4xl?: React.CSSProperties;
    h5xl?: React.CSSProperties;
    h6xl?: React.CSSProperties;
    body1xl?: React.CSSProperties;
    body2xl?: React.CSSProperties;
    body3xl?: React.CSSProperties;
    body3?: React.CSSProperties;
  }

  interface BreakpointOverrides {
    xs: true;
    sm: true;
    md: true;
    lg: true;
    xl: true;
    '2xl': true;
  }
}

declare module '@mui/material/Typography' {
  interface TypographyPropsVariantOverrides {
    h1xl: true;
    h2xl: true;
    h3xl: true;
    h4xl: true;
    h5xl: true;
    h6xl: true;
    body1xl: true;
    body2xl: true;
    body3xl: true;
    body3: true;
  }
}

export const themeOptions: ThemeOptions = {
  palette: {
    mode: 'light',
    primary: {
      main: '#006963',
      contrastText: '#FFFFFF',
      dark: '#003734',
      light: '#E4FFFB',
    },
    secondary: {
      main: '#84C43C',
      dark: '#355D00',
      light: '#EFFFD8',
      contrastText: '#FFFFFF',
    },
    background: {
      default: '#FCFCFB',
      paper: '#FCFCFB',
    },
    text: {
      primary: '#006963',
      secondary: '#69798b',
      disabled: '#8C9291',
    },
    error: {
      main: '#ba1a1a',
      light: '#FFDAD6',
      dark: '#93000A',
      contrastText: '#FFFFFF',
    },
    warning: {
      main: '#F1C21B',
    },
    info: {
      main: '#0244CF',
    },
    success: {
      main: '#25A249',
    },
    link: {
      main: '#00A3D5',
    },
    divider: '#B8C8DC',
  },
  typography: {
    button: {
      textTransform: 'none'
    },
    fontFamily: 'var(--font-inter), system-ui, sans-serif',
  },
};

// Creación del tema con tipografía y breakpoints extendidos
export const materialTheme = createTheme({
  ...themeOptions,
  breakpoints: {
    values: {
      xs: 0,
      sm: 640,
      md: 768,
      lg: 1024,
      xl: 1280,
      '2xl': 1536,
    },
  },
  typography: {
    ...themeOptions.typography,
    h1xl: { fontSize: '4rem', fontWeight: 700 },
    h1: { fontSize: '3rem', fontWeight: 700 },
    h2xl: { fontSize: '2.5rem', fontWeight: 600 },
    h2: { fontSize: '2.25rem', fontWeight: 600 },
    h3xl: { fontSize: '2rem', fontWeight: 600 },
    h3: { fontSize: '1.875rem', fontWeight: 600 },
    h4xl: { fontSize: '1.75rem', fontWeight: 600 },
    h4: { fontSize: '1.5rem', fontWeight: 600 },
    h5xl: { fontSize: '1.5rem', fontWeight: 500 },
    h5: { fontSize: '1.25rem', fontWeight: 500 },
    h6xl: { fontSize: '1.25rem', fontWeight: 500 },
    h6: { fontSize: '1rem', fontWeight: 500},
    body1xl: { fontSize: '1.06rem' },
    body1: { fontSize: '1rem' },
    body2xl: { fontSize: '0.85rem' },
    body2: { fontSize: '0.8rem' },
    body3xl: { fontSize: '0.75rem' },
    body3: { fontSize: '0.65rem', letterSpacing: '0.05em' },
  },
});
