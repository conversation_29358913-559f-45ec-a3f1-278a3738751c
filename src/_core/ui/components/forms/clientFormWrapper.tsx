"use client";

import { DefaultValues } from "react-hook-form";
import { ReactNode } from "react";
import {
  schemas,
  InferSchema,
  EntityKeys,
  ActionKeys,
} from "@/_lib/data/model/schema";
import { FormContextProvider } from "./formContextProvider";
import { FormFields } from "./formFields";
import { FieldMeta } from "./types";
import { LoadStaticDataError } from "@/_core/lib/context/error";

type ClientFormWrapperProps<
  E extends EntityKeys,
  A extends ActionKeys<E>
> = {
  entityName: E;
  actionName: A;
  meta: Record<keyof InferSchema<E, A>, FieldMeta>;
  defaultValues?: DefaultValues<InferSchema<E, A>>;
  action:(formData: FormData) => Promise<unknown>;
  children?: ReactNode;
};

export function ClientFormWrapper<
  E extends EntityKeys,
  A extends ActionKeys<E>
>({
  entityName,
  actionName,
  meta,
  defaultValues,
  action,
  children,
}: ClientFormWrapperProps<E, A>) {
  const schema = schemas?.[entityName]?.[actionName]?.body;

  if (!schema) {
    throw new LoadStaticDataError(`${entityName}.${actionName}`);
  }

  return (
    <FormContextProvider<InferSchema<E, A>>
      schema={schema}
      defaultValues={defaultValues}
      action={action}
      entityName={entityName}
      actionName={actionName}
    >
      <FormFields<InferSchema<E, A>> meta={meta} />
      {children}
    </FormContextProvider>
  );
}
