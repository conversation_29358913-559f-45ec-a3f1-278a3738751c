"use client";

import React, { useState } from "react";

import { iconMap } from "@/_ui/icons/iconMap";
import { RouteExpandable } from "../meta/types";
import RouteDispatcher from "../routeDispatcher";

import {
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Collapse,
  List,
  Box,
} from "@mui/material";

import { ChevronDown } from "react-feather";

type Props = {
  item: RouteExpandable;
};

export default function ExpandableItem({ item }: Props) {
  const [open, setOpen] = useState(false);
  const Icon = item.icon ? iconMap[item.icon] : null;

  return (
    <>
      <ListItem disableGutters>
        <ListItemButton onClick={() => setOpen(!open)}>
          {Icon && (
            <ListItemIcon>
              <Icon size={24} />
            </ListItemIcon>
          )}
          <ListItemText primary={item.label} />
          <Box
            component="span"
            sx={{
              display: "flex",
              alignItems: "center",
              transition: "transform 0.5s",
              transform: open ? "rotate(540deg)" : "rotate(0deg)",
            }}
          >
            <ChevronDown />
          </Box>
        </ListItemButton>
      </ListItem>
      <Collapse
        in={open}
        timeout="auto"
        sx={{ transition: "all 0.5s ease-in-out" }}
        unmountOnExit
      >
        <List disablePadding>
          {item.childs?.map((child, idx) => (
            <RouteDispatcher key={idx} item={child} />
          ))}
        </List>
      </Collapse>
    </>
  );
}
